import { MetadataRoute } from 'next';
import games from '@/data/games';
import { generateSlug } from '@/utils/slug';

export const dynamic = 'force-static';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://playunb.com';
  
  // Base routes
  const routes: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
  ];

  // Add game routes
  games.forEach((game) => {
    const slug = generateSlug(game.name);
    routes.push({
      url: `${baseUrl}/game/${slug}/`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    });
  });

  return routes;
}